"use client"

import * as React from "react"
import { useState } from "react"
import { Search, Plus, X, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { useTags, useCreateTag, useDeleteTag, useTagUsageCount, useTagsWithUsageCounts } from "@/hooks/use-tags"
import { FrontendTag } from "@/lib/api/tag-service"
import { toast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"

// Color options for tags
const TAG_COLORS = [
  '#6366f1', // indigo
  '#8b5cf6', // violet
  '#ec4899', // pink
  '#f59e0b', // amber
  '#10b981', // emerald
  '#3b82f6', // blue
  '#ef4444', // red
  '#f97316', // orange
  '#84cc16', // lime
  '#06b6d4', // cyan
]

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tag: FrontendTag | null
  usageCount: number
  onConfirm: () => void
}

function DeleteConfirmationDialog({ open, onOpenChange, tag, usageCount, onConfirm }: DeleteConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Tag</DialogTitle>
          <DialogDescription>
            Do you really want to delete the tag "{tag?.name}"? 
            {usageCount > 0 && (
              <span className="block mt-2 text-orange-600">
                There are currently {usageCount} task{usageCount !== 1 ? 's' : ''} which have this tag.
              </span>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={onConfirm}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface CreateTagDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

function CreateTagDialog({ open, onOpenChange }: CreateTagDialogProps) {
  const [tagName, setTagName] = useState("")
  const [selectedColor, setSelectedColor] = useState(TAG_COLORS[0])
  const createTagMutation = useCreateTag()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!tagName.trim()) return

    try {
      await createTagMutation.mutateAsync({
        name: tagName.trim(),
        color: selectedColor
      })
      
      toast({
        title: "Success",
        description: "Tag created successfully",
      })
      
      setTagName("")
      setSelectedColor(TAG_COLORS[0])
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create tag. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Tag</DialogTitle>
          <DialogDescription>
            Add a new tag to organize your tasks.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tag-name">Tag Name</Label>
            <Input
              id="tag-name"
              placeholder="Enter tag name..."
              value={tagName}
              onChange={(e) => setTagName(e.target.value)}
              autoFocus
            />
          </div>
          <div className="space-y-2">
            <Label>Color</Label>
            <div className="flex flex-wrap gap-2">
              {TAG_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={cn(
                    "w-8 h-8 rounded-full border-2 transition-all",
                    selectedColor === color ? "border-gray-900 scale-110" : "border-gray-300"
                  )}
                  style={{ backgroundColor: color }}
                  onClick={() => setSelectedColor(color)}
                />
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={!tagName.trim() || createTagMutation.isPending}>
              {createTagMutation.isPending ? "Creating..." : "Create Tag"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export function TagsView() {
  const [searchQuery, setSearchQuery] = useState("")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [deleteTag, setDeleteTag] = useState<FrontendTag | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const { data: tags = [], isLoading: tagsLoading } = useTags()
  const deleteTagMutation = useDeleteTag()

  // Get usage counts for all tags
  const { data: usageCounts = {} } = useTagsWithUsageCounts(tags)

  // Get usage count for the tag being deleted
  const { data: usageData } = useTagUsageCount(deleteTag?.id || "")
  const usageCount = usageData?.usage_count || 0

  // Filter tags based on search query
  const filteredTags = tags.filter((tag) => 
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleDeleteClick = (tag: FrontendTag) => {
    setDeleteTag(tag)
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (!deleteTag) return

    try {
      await deleteTagMutation.mutateAsync(deleteTag.id)
      
      toast({
        title: "Success",
        description: "Tag deleted successfully",
      })
      
      setShowDeleteDialog(false)
      setDeleteTag(null)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete tag. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-none p-6 border-b">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-semibold">Tags</h1>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Tag
          </Button>
        </div>
        
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tags..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tags List */}
      <div className="flex-1 overflow-y-auto p-6">
        {tagsLoading ? (
          <div className="text-center py-8 text-gray-500">Loading tags...</div>
        ) : filteredTags.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchQuery ? "No tags found matching your search." : "No tags yet. Create your first tag!"}
          </div>
        ) : (
          <div className="grid gap-3">
            {filteredTags.map((tag) => (
              <div
                key={tag.id}
                className="flex items-center justify-between p-4 bg-white rounded-lg border hover:shadow-sm transition-shadow"
              >
                <div className="flex items-center gap-3">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: tag.color || '#6366f1' }}
                  />
                  <span className="font-medium">{tag.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {usageCounts[tag.id] || 0} task{(usageCounts[tag.id] || 0) !== 1 ? 's' : ''}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteClick(tag)}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-red-600"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Dialogs */}
      <CreateTagDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog} 
      />
      
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        tag={deleteTag}
        usageCount={usageCount}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  )
}
